import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Shield, 
  ShieldCheck, 
  Star, 
  Home, 
  Calendar, 
  MessageCircle, 
  Award,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { TrustIndicators as TrustData } from '@/types/verification';

interface TrustIndicatorsProps {
  trustData: TrustData;
  showDetailed?: boolean;
  compact?: boolean;
}

const getBadgeIcon = (level: string) => {
  switch (level) {
    case 'gold':
      return <Award className="h-3 w-3" />;
    case 'premium':
      return <ShieldCheck className="h-3 w-3" />;
    case 'basic':
      return <Shield className="h-3 w-3" />;
    default:
      return null;
  }
};

const getBadgeColor = (level: string) => {
  switch (level) {
    case 'gold':
      return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-700';
    case 'premium':
      return 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700';
    case 'basic':
      return 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/20 dark:text-green-300 dark:border-green-700';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600';
  }
};

const getBadgeText = (level: string) => {
  switch (level) {
    case 'gold':
      return 'Gold Verified';
    case 'premium':
      return 'Premium Verified';
    case 'basic':
      return 'Verified';
    default:
      return 'Unverified';
  }
};

const renderStars = (rating: number, compact = false) => {
  const starSize = compact ? 'h-3 w-3' : 'h-4 w-4';
  return Array.from({ length: 5 }, (_, i) => (
    <Star
      key={i}
      className={`${starSize} ${
        i < Math.floor(rating)
          ? 'text-yellow-400 fill-current'
          : i < rating
          ? 'text-yellow-400 fill-current opacity-50'
          : 'text-gray-300 dark:text-gray-600'
      }`}
    />
  ));
};

export const TrustIndicators: React.FC<TrustIndicatorsProps> = ({
  trustData,
  showDetailed = false,
  compact = false
}) => {
  // Debug log to see what data we're getting
  console.log('TrustIndicators received data:', JSON.stringify(trustData, null, 2));
  if (compact) {
    return (
      <div className="flex items-center gap-2 w-full">
        {/* Verification Badge */}
        {trustData.is_verified ? (
          <Badge
            variant="outline"
            className="flex items-center gap-1 text-xs flex-shrink-0 bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-700"
          >
            <ShieldCheck className="h-3 w-3" />
            <span>Verified</span>
          </Badge>
        ) : (
          <Badge
            variant="outline"
            className="flex items-center gap-1 text-xs flex-shrink-0 bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600"
          >
            <AlertTriangle className="h-3 w-3" />
            <span>Unverified</span>
          </Badge>
        )}

        {/* Trust Score Stars - Show even if score is 0 */}
        <div className="flex items-center gap-1 flex-shrink-0 ml-auto">
          <div className="flex items-center">
            {renderStars(trustData.trust_score, true)}
          </div>
          <span className="text-xs text-gray-600 dark:text-gray-300 ml-1 font-medium">
            {trustData.trust_score.toFixed(1)}
          </span>
        </div>
      </div>
    );
  }

  if (!showDetailed) {
    return (
      <div className="flex items-center gap-3">
        {trustData.is_verified ? (
          <Badge 
            variant="outline" 
            className={`${getBadgeColor(trustData.verification_badge_level)} flex items-center gap-1`}
          >
            {getBadgeIcon(trustData.verification_badge_level)}
            {getBadgeText(trustData.verification_badge_level)}
          </Badge>
        ) : (
          <Badge variant="outline" className="flex items-center gap-1 text-gray-600">
            <AlertTriangle className="h-3 w-3" />
            Unverified
          </Badge>
        )}

        {trustData.trust_score > 0 && (
          <div className="flex items-center gap-1">
            {renderStars(trustData.trust_score, false)}
            <span className="text-sm font-medium ml-1">
              {trustData.trust_score.toFixed(1)}
            </span>
          </div>
        )}

        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Home className="h-4 w-4" />
            {trustData.total_properties} properties
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {trustData.years_active} years
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Verification Status */}
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Verification Status</h3>
            {trustData.is_verified ? (
              <Badge 
                variant="outline" 
                className={`${getBadgeColor(trustData.verification_badge_level)} flex items-center gap-1`}
              >
                {getBadgeIcon(trustData.verification_badge_level)}
                {getBadgeText(trustData.verification_badge_level)}
              </Badge>
            ) : (
              <Badge variant="outline" className="flex items-center gap-1 text-gray-600">
                <AlertTriangle className="h-3 w-3" />
                Unverified
              </Badge>
            )}
          </div>

          {/* Trust Score */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Trust Score</span>
              <span className="text-sm font-bold">{trustData.trust_score.toFixed(1)}/5.0</span>
            </div>
            <div className="flex items-center gap-1">
              {renderStars(trustData.trust_score, false)}
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Home className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Properties</span>
              </div>
              <p className="font-medium">{trustData.total_properties}</p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Successful Rentals</span>
              </div>
              <p className="font-medium">{trustData.successful_rentals}</p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Years Active</span>
              </div>
              <p className="font-medium">{trustData.years_active}</p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Response Rate</span>
              </div>
              <p className="font-medium">{trustData.response_rate}%</p>
            </div>
          </div>

          {/* Verification Benefits */}
          {trustData.is_verified && (
            <div className="pt-3 border-t">
              <h4 className="text-sm font-medium mb-2">Verification Benefits</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Identity verified by our team
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Property ownership confirmed
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Enhanced profile visibility
                </div>
                {trustData.verification_badge_level === 'premium' && (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3 text-blue-600" />
                    Priority customer support
                  </div>
                )}
                {trustData.verification_badge_level === 'gold' && (
                  <>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-yellow-600" />
                      Featured in search results
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-yellow-600" />
                      Dedicated account manager
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Warning for Unverified */}
          {!trustData.is_verified && (
            <div className="pt-3 border-t">
              <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Unverified Landlord</p>
                  <p className="text-yellow-700">
                    This landlord has not completed identity verification. 
                    Exercise caution and verify their identity independently.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
